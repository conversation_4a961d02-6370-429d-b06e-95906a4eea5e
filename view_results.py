import json
import numpy as np
from datetime import datetime

def load_and_display_results(json_file="detection_results.json"):
    """加载并显示检测结果"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            detection_data = json.load(f)
        
        print("=" * 60)
        print("视频检测结果详细信息")
        print("=" * 60)
        
        total_frames = len(detection_data)
        total_detections = sum(len(frame["detections"]) for frame in detection_data)
        
        print(f"文件: {json_file}")
        print(f"总帧数: {total_frames}")
        print(f"总检测数: {total_detections}")
        
        if total_detections > 0:
            # 统计各类别检测数量和置信度
            class_stats = {}
            all_confidences = []
            
            for frame_idx, frame in enumerate(detection_data):
                for detection in frame["detections"]:
                    class_name = detection["class_name"]
                    confidence = detection["confidence"]
                    bbox = detection["bbox"]
                    
                    if class_name not in class_stats:
                        class_stats[class_name] = {
                            'count': 0,
                            'confidences': [],
                            'bbox_areas': []
                        }
                    
                    class_stats[class_name]['count'] += 1
                    class_stats[class_name]['confidences'].append(confidence)
                    
                    # 计算边界框面积
                    width = bbox[2] - bbox[0]
                    height = bbox[3] - bbox[1]
                    area = width * height
                    class_stats[class_name]['bbox_areas'].append(area)
                    
                    all_confidences.append(confidence)
            
            print(f"\n各类别详细统计:")
            for class_name, stats in sorted(class_stats.items()):
                count = stats['count']
                confidences = stats['confidences']
                areas = stats['bbox_areas']
                
                avg_conf = np.mean(confidences)
                max_conf = max(confidences)
                min_conf = min(confidences)
                
                avg_area = np.mean(areas)
                max_area = max(areas)
                min_area = min(areas)
                
                print(f"\n  {class_name}:")
                print(f"    检测次数: {count}")
                print(f"    置信度 - 平均: {avg_conf:.3f}, 最高: {max_conf:.3f}, 最低: {min_conf:.3f}")
                print(f"    边界框面积 - 平均: {avg_area:.1f}, 最大: {max_area:.1f}, 最小: {min_area:.1f}")
            
            # 整体统计
            if all_confidences:
                print(f"\n整体置信度统计:")
                print(f"  平均置信度: {np.mean(all_confidences):.3f}")
                print(f"  置信度标准差: {np.std(all_confidences):.3f}")
                print(f"  最高置信度: {max(all_confidences):.3f}")
                print(f"  最低置信度: {min(all_confidences):.3f}")
            
            # 每帧检测数量统计
            detections_per_frame = [len(frame["detections"]) for frame in detection_data]
            print(f"\n每帧检测数量统计:")
            print(f"  平均每帧检测数: {np.mean(detections_per_frame):.2f}")
            print(f"  最多检测数: {max(detections_per_frame)}")
            print(f"  最少检测数: {min(detections_per_frame)}")
            
            # 显示前几帧的详细信息
            print(f"\n前5帧详细信息:")
            for i, frame in enumerate(detection_data[:5]):
                print(f"\n  帧 {i}:")
                print(f"    时间戳: {frame['timestamp']}")
                print(f"    检测数量: {len(frame['detections'])}")
                for j, detection in enumerate(frame['detections']):
                    bbox = detection['bbox']
                    print(f"      检测 {j+1}: {detection['class_name']} "
                          f"(置信度: {detection['confidence']:.3f}, "
                          f"位置: [{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}])")
        
        print("=" * 60)
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {json_file}")
    except json.JSONDecodeError:
        print(f"错误: {json_file} 不是有效的JSON文件")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    load_and_display_results()
