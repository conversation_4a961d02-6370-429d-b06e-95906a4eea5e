# 飞机检测项目信息

## 项目概述
本项目使用YOLO11模型对视频中的飞机进行检测，并保存检测结果供后续分析。

## 项目结构
```
airplane/
├── app.py                    # 主检测程序
├── view_results.py          # 结果查看程序
├── requirements.txt         # 依赖包列表
├── yolo11s.pt              # YOLO11模型文件
├── detection_results.json  # 检测结果JSON文件
├── project_info.md         # 项目信息文档
└── runs/                   # YOLO输出目录
    └── detect/
        └── predict3/
            └── gettyimages-88031721-640_adpp.avi  # 检测结果视频
```

## 功能特性

### 1. 视频检测 (app.py)
- 使用YOLO11s模型进行飞机检测
- 支持CPU推理，避免CUDA兼容性问题
- 实时显示检测进度
- 自动保存检测结果视频
- 将检测数据保存为JSON格式

### 2. 结果分析 (view_results.py)
- 加载并分析检测结果
- 统计各类别检测数量
- 计算置信度统计信息
- 分析边界框面积分布
- 显示每帧详细检测信息

## 检测结果统计

### 基本信息
- **处理帧数**: 100帧 (限制处理以避免内存问题)
- **总检测数**: 207次
- **检测类别**: airplane (飞机)
- **平均每帧检测数**: 2.07个

### 置信度统计
- **平均置信度**: 0.802
- **置信度标准差**: 0.135
- **最高置信度**: 0.925
- **最低置信度**: 0.265

### 边界框统计
- **平均面积**: 99,592.9 像素²
- **最大面积**: 200,415.0 像素²
- **最小面积**: 1,111.7 像素²

## 输出文件说明

### detection_results.json
JSON格式的检测结果文件，包含以下信息：
- `frame_index`: 帧索引
- `timestamp`: 检测时间戳
- `detections`: 检测结果数组
  - `bbox`: 边界框坐标 [x1, y1, x2, y2]
  - `confidence`: 置信度分数
  - `class_id`: 类别ID
  - `class_name`: 类别名称

### 检测结果视频
保存在 `runs/detect/predict3/` 目录下，包含：
- 原始视频的检测结果可视化
- 边界框和置信度标注
- AVI格式输出

## 使用方法

### 运行检测
```bash
python app.py
```

### 查看结果
```bash
python view_results.py
```

## 技术细节

### 模型配置
- **模型**: YOLO11s (yolo11s.pt)
- **推理设备**: CPU (避免CUDA问题)
- **最大检测数**: 100个对象/帧
- **处理限制**: 100帧 (可调整)

### 依赖包
- torch: PyTorch深度学习框架
- ultralytics: YOLO实现
- numpy: 数值计算
- opencv-python: 视频处理

## 性能优化
1. **内存管理**: 限制处理帧数避免内存溢出
2. **CPU推理**: 使用CPU避免CUDA兼容性问题
3. **流式处理**: 使用stream=True进行流式预测
4. **错误处理**: 添加异常捕获和错误报告

## 后续改进建议
1. 支持批量视频处理
2. 添加检测结果可视化界面
3. 实现检测结果的统计图表
4. 支持自定义检测参数配置
5. 添加检测结果的导出功能（CSV、Excel等）
