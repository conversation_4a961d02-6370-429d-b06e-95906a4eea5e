import numpy as np
import torch
from ultralytics import YOLO
import json
from datetime import datetime


def save_detection_results(results, output_file="detection_results.json"):
    """保存检测结果到JSON文件"""
    detection_data = []

    for i, result in enumerate(results):
        frame_data = {
            "frame_index": i,
            "timestamp": datetime.now().isoformat(),
            "detections": []
        }

        if result.boxes is not None:
            boxes = result.boxes
            for j in range(len(boxes)):
                detection = {
                    "bbox": boxes.xyxy[j].cpu().numpy().tolist(),  # [x1, y1, x2, y2]
                    "confidence": float(boxes.conf[j].cpu().numpy()),
                    "class_id": int(boxes.cls[j].cpu().numpy()),
                    "class_name": result.names[int(boxes.cls[j].cpu().numpy())]
                }
                frame_data["detections"].append(detection)

        detection_data.append(frame_data)

    # 保存到JSON文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(detection_data, f, indent=2, ensure_ascii=False)

    return detection_data


def print_detection_info(detection_data):
    """打印检测信息统计"""
    print("=" * 50)
    print("视频检测结果统计")
    print("=" * 50)

    total_frames = len(detection_data)
    total_detections = sum(len(frame["detections"]) for frame in detection_data)

    print(f"总帧数: {total_frames}")
    print(f"总检测数: {total_detections}")

    if total_detections > 0:
        # 统计各类别检测数量
        class_counts = {}
        confidence_scores = []

        for frame in detection_data:
            for detection in frame["detections"]:
                class_name = detection["class_name"]
                confidence = detection["confidence"]

                class_counts[class_name] = class_counts.get(class_name, 0) + 1
                confidence_scores.append(confidence)

        print("\n各类别检测统计:")
        for class_name, count in sorted(class_counts.items()):
            print(f"  {class_name}: {count} 次")

        if confidence_scores:
            avg_confidence = np.mean(confidence_scores)
            max_confidence = max(confidence_scores)
            min_confidence = min(confidence_scores)

            print(f"\n置信度统计:")
            print(f"  平均置信度: {avg_confidence:.3f}")
            print(f"  最高置信度: {max_confidence:.3f}")
            print(f"  最低置信度: {min_confidence:.3f}")

    print("=" * 50)


# 加载模型
model = YOLO('yolo11s.pt')

# 进行预测
video_path = r'c:\Users\<USER>\Desktop\新建文件夹\gettyimages-88031721-640_adpp.mp4'
print(f"开始处理视频: {video_path}")

try:
    # 强制使用CPU进行推理，限制处理帧数避免内存问题
    print("正在进行检测...")
    results = model.predict(video_path, save=True, stream=True, device='cpu', max_det=100)

    # 收集结果，但限制数量避免内存问题
    all_results = []
    frame_count = 0
    max_frames = 100  # 限制处理的帧数

    for result in results:
        all_results.append(result)
        frame_count += 1
        if frame_count % 10 == 0:
            print(f"已处理 {frame_count} 帧")
        if frame_count >= max_frames:
            print(f"达到最大处理帧数限制 ({max_frames})，停止处理")
            break

    print(f"总共处理了 {len(all_results)} 帧")

    # 保存检测结果
    output_file = "detection_results.json"
    detection_data = save_detection_results(all_results, output_file)
    print(f"\n检测结果已保存到: {output_file}")

    # 打印检测信息
    print_detection_info(detection_data)

    print(f"\n处理完成！检测结果视频保存在 runs/detect/ 目录下")

except Exception as e:
    print(f"处理过程中出现错误: {e}")
    import traceback
    traceback.print_exc()